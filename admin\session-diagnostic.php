<?php
/**
 * Session Diagnostic Tool
 * Flix Salonce - PHP Version
 * Use this to diagnose session issues in production
 */

require_once __DIR__ . '/../config/app.php';

// Check if user is admin (allow access for debugging)
if (!isLoggedIn() || !hasRole('ADMIN')) {
    // For debugging purposes, show limited info even if not logged in
    if (!isLoggedIn()) {
        echo "<h2>Not logged in - Session Diagnostic</h2>";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Diagnostic - Flix Salon & SPA</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #333; border-bottom: 2px solid #d4af37; padding-bottom: 5px; }
        .info-grid { display: grid; grid-template-columns: 200px 1fr; gap: 10px; }
        .info-grid div:nth-child(odd) { font-weight: bold; }
        .status-ok { color: green; }
        .status-warning { color: orange; }
        .status-error { color: red; }
        .code { background: #f8f8f8; padding: 10px; border-radius: 3px; font-family: monospace; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Session Diagnostic Tool</h1>
        <p><strong>Current Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
        <p><strong>Environment:</strong> <?= isProduction() ? 'PRODUCTION' : 'DEVELOPMENT' ?></p>

        <!-- Session Configuration -->
        <div class="section">
            <h3>Session Configuration</h3>
            <div class="info-grid">
                <div>Session Status:</div>
                <div class="<?= session_status() === PHP_SESSION_ACTIVE ? 'status-ok' : 'status-error' ?>">
                    <?= session_status() === PHP_SESSION_ACTIVE ? 'ACTIVE' : 'INACTIVE' ?>
                </div>
                
                <div>Session ID:</div>
                <div><?= session_id() ?: 'Not set' ?></div>
                
                <div>Session Name:</div>
                <div><?= session_name() ?></div>
                
                <div>Cookie Lifetime:</div>
                <div><?= ini_get('session.cookie_lifetime') ?> seconds (<?= round(ini_get('session.cookie_lifetime') / 86400, 1) ?> days)</div>
                
                <div>GC Max Lifetime:</div>
                <div><?= ini_get('session.gc_maxlifetime') ?> seconds (<?= round(ini_get('session.gc_maxlifetime') / 86400, 1) ?> days)</div>
                
                <div>Cookie Secure:</div>
                <div class="<?= ini_get('session.cookie_secure') ? 'status-ok' : 'status-warning' ?>">
                    <?= ini_get('session.cookie_secure') ? 'YES' : 'NO' ?>
                </div>
                
                <div>Cookie HTTPOnly:</div>
                <div class="<?= ini_get('session.cookie_httponly') ? 'status-ok' : 'status-error' ?>">
                    <?= ini_get('session.cookie_httponly') ? 'YES' : 'NO' ?>
                </div>
                
                <div>Cookie SameSite:</div>
                <div><?= ini_get('session.cookie_samesite') ?: 'Not set' ?></div>
            </div>
        </div>

        <!-- HTTPS Detection -->
        <div class="section">
            <h3>HTTPS Detection</h3>
            <div class="info-grid">
                <div>$_SERVER['HTTPS']:</div>
                <div><?= $_SERVER['HTTPS'] ?? 'Not set' ?></div>
                
                <div>$_SERVER['HTTP_X_FORWARDED_PROTO']:</div>
                <div><?= $_SERVER['HTTP_X_FORWARDED_PROTO'] ?? 'Not set' ?></div>
                
                <div>$_SERVER['HTTP_X_FORWARDED_SSL']:</div>
                <div><?= $_SERVER['HTTP_X_FORWARDED_SSL'] ?? 'Not set' ?></div>
                
                <div>$_SERVER['SERVER_PORT']:</div>
                <div><?= $_SERVER['SERVER_PORT'] ?? 'Not set' ?></div>
                
                <div>Detected HTTPS:</div>
                <div class="<?= 
                    ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ||
                     (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
                     (isset($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on') ||
                     (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443)) ? 'status-ok' : 'status-warning' 
                ?>">
                    <?= 
                        ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ||
                         (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
                         (isset($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on') ||
                         (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443)) ? 'YES' : 'NO' 
                    ?>
                </div>
            </div>
        </div>

        <!-- Session Data -->
        <div class="section">
            <h3>Current Session Data</h3>
            <?php if (!empty($_SESSION)): ?>
                <div class="info-grid">
                    <?php foreach ($_SESSION as $key => $value): ?>
                        <div><?= htmlspecialchars($key) ?>:</div>
                        <div>
                            <?php if ($key === 'session_token'): ?>
                                <?= substr($value, 0, 8) ?>...
                            <?php elseif (is_array($value)): ?>
                                <?= htmlspecialchars(json_encode($value)) ?>
                            <?php else: ?>
                                <?= htmlspecialchars($value) ?>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="status-warning">No session data found</p>
            <?php endif; ?>
        </div>

        <!-- Authentication Status -->
        <div class="section">
            <h3>Authentication Status</h3>
            <div class="info-grid">
                <div>isLoggedIn():</div>
                <div class="<?= isLoggedIn() ? 'status-ok' : 'status-error' ?>">
                    <?= isLoggedIn() ? 'YES' : 'NO' ?>
                </div>
                
                <div>Auth Object Exists:</div>
                <div class="<?= isset($auth) ? 'status-ok' : 'status-error' ?>">
                    <?= isset($auth) ? 'YES' : 'NO' ?>
                </div>
                
                <?php if (isset($auth)): ?>
                <div>$auth->isAuthenticated():</div>
                <div class="<?= $auth->isAuthenticated() ? 'status-ok' : 'status-error' ?>">
                    <?= $auth->isAuthenticated() ? 'YES' : 'NO' ?>
                </div>
                <?php endif; ?>
                
                <?php if (isLoggedIn()): ?>
                <div>User Role:</div>
                <div><?= $_SESSION['user_role'] ?? 'Not set' ?></div>
                
                <div>hasRole('ADMIN'):</div>
                <div class="<?= hasRole('ADMIN') ? 'status-ok' : 'status-warning' ?>">
                    <?= hasRole('ADMIN') ? 'YES' : 'NO' ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Database Sessions -->
        <div class="section">
            <h3>Database Sessions (Last 10)</h3>
            <?php
            try {
                $sessions = $database->fetchAll("SELECT id, session_token, user_id, expires, created_at FROM sessions ORDER BY created_at DESC LIMIT 10");
                if ($sessions):
            ?>
                <table>
                    <thead>
                        <tr>
                            <th>Token (First 8)</th>
                            <th>User ID</th>
                            <th>Expires</th>
                            <th>Status</th>
                            <th>Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sessions as $session): ?>
                            <?php 
                            $isExpired = strtotime($session['expires']) < time();
                            $isCurrent = isset($_SESSION['session_token']) && $_SESSION['session_token'] === $session['session_token'];
                            ?>
                            <tr style="<?= $isCurrent ? 'background-color: #e8f5e8;' : '' ?>">
                                <td><?= substr($session['session_token'], 0, 8) ?>...</td>
                                <td><?= htmlspecialchars($session['user_id']) ?></td>
                                <td><?= $session['expires'] ?></td>
                                <td class="<?= $isExpired ? 'status-error' : 'status-ok' ?>">
                                    <?= $isExpired ? 'EXPIRED' : 'ACTIVE' ?>
                                    <?= $isCurrent ? ' (CURRENT)' : '' ?>
                                </td>
                                <td><?= $session['created_at'] ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p class="status-warning">No sessions found in database</p>
            <?php endif; ?>
            <?php } catch (Exception $e): ?>
                <p class="status-error">Error fetching sessions: <?= htmlspecialchars($e->getMessage()) ?></p>
            <?php endtry; ?>
        </div>

        <!-- Cookies -->
        <div class="section">
            <h3>Cookies</h3>
            <?php if (!empty($_COOKIE)): ?>
                <div class="info-grid">
                    <?php foreach ($_COOKIE as $name => $value): ?>
                        <div><?= htmlspecialchars($name) ?>:</div>
                        <div>
                            <?php if (strpos($name, 'PHPSESSID') !== false): ?>
                                <?= substr($value, 0, 8) ?>...
                            <?php else: ?>
                                <?= htmlspecialchars(substr($value, 0, 50)) ?><?= strlen($value) > 50 ? '...' : '' ?>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="status-warning">No cookies found</p>
            <?php endif; ?>
        </div>

        <div class="section">
            <h3>Actions</h3>
            <p><a href="<?= getBasePath() ?>/admin">← Back to Admin Dashboard</a></p>
            <p><a href="?refresh=1">🔄 Refresh Diagnostic</a></p>
        </div>
    </div>
</body>
</html>
