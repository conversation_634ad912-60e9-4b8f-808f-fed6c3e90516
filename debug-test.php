<?php
/**
 * Debug Test - Check what's working and what's not
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Test</h1>";

echo "<h2>1. Basic PHP</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current Time: " . date('Y-m-d H:i:s') . "<br>";

echo "<h2>2. File Includes</h2>";
try {
    echo "Attempting to include config/app.php...<br>";
    require_once 'config/app.php';
    echo "✅ config/app.php included successfully<br>";
} catch (Exception $e) {
    echo "❌ Error including config/app.php: " . $e->getMessage() . "<br>";
}

echo "<h2>3. Functions</h2>";
if (function_exists('isProduction')) {
    echo "✅ isProduction() function exists<br>";
    echo "isProduction() result: " . (isProduction() ? 'true' : 'false') . "<br>";
} else {
    echo "❌ isProduction() function not found<br>";
}

if (function_exists('getBasePath')) {
    echo "✅ getBasePath() function exists<br>";
    echo "getBasePath() result: '" . getBasePath() . "'<br>";
} else {
    echo "❌ getBasePath() function not found<br>";
}

if (function_exists('getBaseUrl')) {
    echo "✅ getBaseUrl() function exists<br>";
    echo "getBaseUrl() result: '" . getBaseUrl() . "'<br>";
} else {
    echo "❌ getBaseUrl() function not found<br>";
}

if (function_exists('isLoggedIn')) {
    echo "✅ isLoggedIn() function exists<br>";
    echo "isLoggedIn() result: " . (isLoggedIn() ? 'true' : 'false') . "<br>";
} else {
    echo "❌ isLoggedIn() function not found<br>";
}

if (function_exists('hasRole')) {
    echo "✅ hasRole() function exists<br>";
} else {
    echo "❌ hasRole() function not found<br>";
}

echo "<h2>4. Database</h2>";
if (isset($database)) {
    echo "✅ \$database object exists<br>";
    try {
        $result = $database->fetch("SELECT 1 as test");
        echo "✅ Database connection working<br>";
    } catch (Exception $e) {
        echo "❌ Database connection error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ \$database object not found<br>";
}

echo "<h2>5. Auth Object</h2>";
if (isset($auth)) {
    echo "✅ \$auth object exists<br>";
    echo "Auth class: " . get_class($auth) . "<br>";
} else {
    echo "❌ \$auth object not found<br>";
}

echo "<h2>6. Session</h2>";
echo "Session status: " . session_status() . " (1=disabled, 2=active)<br>";
echo "Session ID: " . session_id() . "<br>";
echo "Session name: " . session_name() . "<br>";

if (!empty($_SESSION)) {
    echo "Session data:<br>";
    foreach ($_SESSION as $key => $value) {
        if ($key === 'session_token') {
            echo "- $key: " . substr($value, 0, 8) . "...<br>";
        } else {
            echo "- $key: " . htmlspecialchars($value) . "<br>";
        }
    }
} else {
    echo "No session data<br>";
}

echo "<h2>7. Server Variables</h2>";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'not set') . "<br>";
echo "HTTPS: " . ($_SERVER['HTTPS'] ?? 'not set') . "<br>";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'not set') . "<br>";
echo "DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'not set') . "<br>";

echo "<h2>8. Cookies</h2>";
if (!empty($_COOKIE)) {
    foreach ($_COOKIE as $name => $value) {
        if (strpos($name, 'PHPSESSID') !== false) {
            echo "- $name: " . substr($value, 0, 8) . "...<br>";
        } else {
            echo "- $name: " . htmlspecialchars(substr($value, 0, 30)) . "<br>";
        }
    }
} else {
    echo "No cookies found<br>";
}

echo "<h2>9. Error Log Check</h2>";
$errorLogPath = 'logs/error.log';
if (file_exists($errorLogPath)) {
    echo "✅ Error log exists<br>";
    $lastLines = array_slice(file($errorLogPath), -5);
    echo "Last 5 lines:<br>";
    foreach ($lastLines as $line) {
        echo htmlspecialchars($line) . "<br>";
    }
} else {
    echo "❌ Error log not found at: $errorLogPath<br>";
}

echo "<h2>10. File Permissions</h2>";
$files = ['config/app.php', 'includes/auth.php', 'config/database.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists (permissions: " . substr(sprintf('%o', fileperms($file)), -4) . ")<br>";
    } else {
        echo "❌ $file not found<br>";
    }
}

?>
