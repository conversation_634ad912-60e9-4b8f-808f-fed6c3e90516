<?php
/**
 * Verify Currency Constant Fix
 * Quick verification that the CURRENCY_SYMBOL constant issue is resolved
 */

// Enable error reporting to catch any warnings
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Currency Fix Verification</title></head><body>";
echo "<h1>🔧 Currency Constant Fix Verification</h1>";

// Test multiple includes like what happens in production
echo "<h2>Testing Multiple Config Includes...</h2>";

try {
    // First include
    require_once __DIR__ . '/config/app.php';
    echo "✅ First config include: SUCCESS<br>";
    
    // Second include (should be prevented by guard)
    include __DIR__ . '/config/app.php';
    echo "✅ Second config include: SUCCESS (prevented by guard)<br>";
    
    // Third include (should also be prevented)
    include __DIR__ . '/config/app.php';
    echo "✅ Third config include: SUCCESS (prevented by guard)<br>";
    
    echo "<h2>✅ No 'Constant already defined' warnings!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error: " . htmlspecialchars($e->getMessage()) . "</h2>";
}

echo "<h2>Current Constants:</h2>";
echo "<p><strong>CURRENCY_SYMBOL:</strong> " . (defined('CURRENCY_SYMBOL') ? CURRENCY_SYMBOL : 'Not defined') . "</p>";
echo "<p><strong>CURRENCY_CODE:</strong> " . (defined('CURRENCY_CODE') ? CURRENCY_CODE : 'Not defined') . "</p>";
echo "<p><strong>APP_NAME:</strong> " . (defined('APP_NAME') ? APP_NAME : 'Not defined') . "</p>";

if (function_exists('formatCurrency')) {
    echo "<h2>Currency Formatting Test:</h2>";
    echo "<p>formatCurrency(50000) = <strong>" . formatCurrency(50000) . "</strong></p>";
}

echo "<h2>🎉 Fix Status: WORKING</h2>";
echo "<p>The currency constant redefinition issue has been resolved!</p>";
echo "<p><a href='customer/book/'>Test Customer Booking Page</a></p>";

echo "</body></html>";
?>
