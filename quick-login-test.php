<?php
/**
 * Quick Login Test
 * Test login functionality and session persistence
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/app.php';

// Handle login form submission
if ($_POST['action'] ?? '' === 'login') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($email && $password && isset($auth)) {
        $result = $auth->login($email, $password);
        if ($result['success']) {
            $message = "✅ Login successful! Redirecting...";
            echo "<script>setTimeout(() => window.location.reload(), 2000);</script>";
        } else {
            $message = "❌ Login failed: " . ($result['error'] ?? 'Unknown error');
        }
    } else {
        $message = "❌ Please provide email and password";
    }
}

// Handle logout
if ($_GET['action'] ?? '' === 'logout') {
    if (isset($auth)) {
        $auth->logout();
        $message = "✅ Logged out successfully";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Login Test - Flix Salon & SPA</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .status-ok { color: green; font-weight: bold; }
        .status-error { color: red; font-weight: bold; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .button { padding: 10px 20px; background: #d4af37; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .button:hover { background: #b8941f; }
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        .code { background: #f8f8f8; padding: 10px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Quick Login Test</h1>
        
        <?php if (isset($message)): ?>
            <div class="section">
                <p><?= $message ?></p>
            </div>
        <?php endif; ?>

        <div class="section">
            <h3>System Status</h3>
            <div class="code">
                <strong>Database:</strong> <?= isset($database) ? '✅ Connected' : '❌ Not connected' ?><br>
                <strong>Auth Object:</strong> <?= isset($auth) ? '✅ Available' : '❌ Not available' ?><br>
                <strong>Session Status:</strong> <?= session_status() === PHP_SESSION_ACTIVE ? '✅ Active' : '❌ Inactive' ?><br>
                <strong>Session ID:</strong> <?= session_id() ?: 'Not set' ?><br>
                <strong>Environment:</strong> <?= isProduction() ? 'PRODUCTION' : 'DEVELOPMENT' ?>
            </div>
        </div>

        <?php if (isLoggedIn()): ?>
            <div class="section">
                <h3 class="status-ok">✅ You are logged in!</h3>
                <div class="code">
                    <strong>User ID:</strong> <?= $_SESSION['user_id'] ?? 'Not set' ?><br>
                    <strong>User Role:</strong> <?= $_SESSION['user_role'] ?? 'Not set' ?><br>
                    <strong>User Name:</strong> <?= $_SESSION['user_name'] ?? 'Not set' ?><br>
                    <strong>Session Token:</strong> <?= isset($_SESSION['session_token']) ? substr($_SESSION['session_token'], 0, 8) . '...' : 'Not set' ?><br>
                    <strong>Last Activity:</strong> <?= isset($_SESSION['last_activity']) ? date('Y-m-d H:i:s', $_SESSION['last_activity']) : 'Not set' ?>
                </div>
                
                <p>
                    <a href="test-session-persistence.php" class="button">Test Session Persistence</a>
                    <a href="?action=logout" class="button danger">Logout</a>
                </p>
                
                <?php if (hasRole('ADMIN')): ?>
                    <p><a href="admin/session-diagnostic.php" class="button">Admin Diagnostics</a></p>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="section">
                <h3>Login</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="login">
                    
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <button type="submit" class="button">Login</button>
                </form>
                
                <p><small>Use your admin credentials to test the session system.</small></p>
            </div>
        <?php endif; ?>

        <div class="section">
            <h3>Session Configuration</h3>
            <div class="code">
                <strong>Cookie Lifetime:</strong> <?= ini_get('session.cookie_lifetime') ?> seconds (<?= round(ini_get('session.cookie_lifetime') / 86400, 1) ?> days)<br>
                <strong>GC Max Lifetime:</strong> <?= ini_get('session.gc_maxlifetime') ?> seconds<br>
                <strong>Cookie Secure:</strong> <?= ini_get('session.cookie_secure') ? 'YES' : 'NO' ?><br>
                <strong>Cookie HTTPOnly:</strong> <?= ini_get('session.cookie_httponly') ? 'YES' : 'NO' ?><br>
                <strong>Cookie SameSite:</strong> <?= ini_get('session.cookie_samesite') ?: 'Not set' ?>
            </div>
        </div>

        <div class="section">
            <h3>Current Cookies</h3>
            <?php if (!empty($_COOKIE)): ?>
                <div class="code">
                    <?php foreach ($_COOKIE as $name => $value): ?>
                        <strong><?= htmlspecialchars($name) ?>:</strong> 
                        <?php if (strpos($name, 'PHPSESSID') !== false): ?>
                            <?= substr($value, 0, 8) ?>...
                        <?php else: ?>
                            <?= htmlspecialchars(substr($value, 0, 30)) ?><?= strlen($value) > 30 ? '...' : '' ?>
                        <?php endif; ?><br>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p>No cookies found</p>
            <?php endif; ?>
        </div>

        <div class="section">
            <h3>Quick Links</h3>
            <p>
                <a href="debug-test.php" class="button">Debug Test</a>
                <a href="test-session-persistence.php" class="button">Session Persistence Test</a>
                <?php if (hasRole('ADMIN')): ?>
                    <a href="admin/session-diagnostic.php" class="button">Admin Diagnostics</a>
                <?php endif; ?>
            </p>
        </div>
    </div>
</body>
</html>
